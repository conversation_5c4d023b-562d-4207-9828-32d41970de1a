"use client";

import { UploadType } from "@/types";

interface FileUploaderProps {
  files: FileList | null;
  uploadType: UploadType;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function FileUploader({
  files,
  uploadType,
  handleFileChange,
}: FileUploaderProps) {
  return (
    <div className="w-full max-w-md">
      <input
        type="file"
        accept={
          uploadType === "pdf" ? ".pdf" : "image/png, image/jpeg, image/webp"
        }
        multiple={uploadType === "images"}
        onChange={handleFileChange}
        className="hidden"
        id="file-upload"
      />
      <label
        htmlFor="file-upload"
        className="cursor-pointer block w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-all"
      >
        {files ? (
          <div>
            {uploadType === "pdf" ? (
              <span className="text-blue-600">{files[0].name}</span>
            ) : (
              <div className="space-y-2">
                <span className="text-blue-600">
                  {files.length} 枚の画像が選択されました
                </span>
                <div className="text-sm text-gray-500">
                  {Array.from(files).map((file, index) => (
                    <div key={index}>
                      {file.name} (
                      {file.type.replace("image/", "").toUpperCase()})
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div>
            <div className="text-gray-600">
              クリックしてファイルをアップロード
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {uploadType === "pdf"
                ? "PDFファイルのみ"
                : "JPG, PNG, WEBPファイルのみ"}
            </div>
          </div>
        )}
      </label>
    </div>
  );
}
