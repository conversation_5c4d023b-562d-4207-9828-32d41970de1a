import { NextResponse } from "next/server";
import OpenAI from "openai";
import { zodTextFormat } from "openai/helpers/zod";
import { z } from "zod";
import { GoogleGenAI, Type } from "@google/genai";
import Anthropic from "@anthropic-ai/sdk";
import fs from "fs/promises";
import path from "path";

const paragraphSchema = z.object({
  type: z.enum(["lines", "lyrics", "directions"]),
  header: z.string(),
  content: z.string(),
});

const scriptSchema = z.object({
  paragraphs: paragraphSchema.array(),
});

type Paragraph = z.infer<typeof paragraphSchema>;
type Script = z.infer<typeof scriptSchema>;

export type { Paragraph, Script };

const gemini = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const model = formData.get("model") as string;
    const imageData = formData.get("image") as string;

    if (!imageData) {
      return NextResponse.json(
        { error: "Image data is required" },
        { status: 400 }
      );
    }

    // Extract base64 data from data URL
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, "");
    const targetImage = Buffer.from(base64Data, "base64").buffer;

    const instruction =
      "優秀な画像認識AIとして、与えられた画像からテキストを抽出し、構造化されたJSONデータに変換しなさい。";
    const prompt = await fs.readFile(
      path.join(process.cwd(), "src/app/api/recognize/prompt.md"),
      "utf-8"
    );

    let analysis: Script;

    switch (model.split("-")[0]) {
      case "gemini": {
        const response = await gemini.models.generateContent({
          model: model,
          contents: [
            {
              inlineData: {
                mimeType: "image/jpeg" as const,
                data: Buffer.from(targetImage).toString("base64"),
              },
            },
            { text: prompt },
          ],
          config: {
            temperature: 0.2,
            systemInstruction: instruction,
            responseMimeType: "application/json",
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                paragraphs: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      type: {
                        type: Type.STRING,
                        description:
                          'Paragraph type ("lines" | "lyrics" | "directions")',
                        nullable: false,
                      },
                      header: {
                        type: Type.STRING,
                        description: "Name or symbol",
                        nullable: false,
                      },
                      content: {
                        type: Type.STRING,
                        description: "Paragraph content",
                        nullable: false,
                      },
                    },
                    required: ["type", "header", "content"],
                    propertyOrdering: ["type", "header", "content"],
                  },
                },
              },
              required: ["paragraphs"],
              propertyOrdering: ["paragraphs"],
            },
          },
        });
        analysis = JSON.parse(response.text ?? "{}") as Script;
        break;
      }

      case "claude": {
        const response = await anthropic.messages.create({
          model: model,
          max_tokens: 8192,
          temperature: 0.2,
          system: instruction,
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/jpeg" as const,
                    data: Buffer.from(targetImage).toString("base64"),
                  },
                },
                {
                  type: "text",
                  text: prompt,
                },
              ],
            },
          ],
        });
        analysis = JSON.parse(
          response.content[0].type === "text" ? response.content[0].text : "{}"
        ) as Script;
        break;
      }

      default: {
        // OpenAI
        const response = await openai.responses.parse({
          model: model,
          temperature: model.startsWith("gpt") ? 0.2 : null,
          instructions: instruction,
          input: [
            {
              role: "user",
              content: [
                { type: "input_text", text: prompt },
                {
                  type: "input_image" as const,
                  image_url: `data:image/jpeg;base64,${Buffer.from(
                    targetImage
                  ).toString("base64")}`,
                  detail: "auto" as const,
                },
              ],
            },
          ],
          text: {
            format: zodTextFormat(scriptSchema, "analysis"),
          },
        });
        analysis = response.output_parsed ?? ({} as Script);
        break;
      }
    }

    console.log(analysis);
    return NextResponse.json({ analysis });
  } catch (error) {
    console.error("Error processing files:", error);
    return NextResponse.json(
      { error: "Failed to process files" },
      { status: 500 }
    );
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
};
