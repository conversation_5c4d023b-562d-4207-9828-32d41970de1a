"use client";

import ModelSelector from "@/components/ModelSelector";
import FileUploader from "@/components/FileUploader";
import MessageDisplay from "@/components/MessageDisplay";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { <PERSON>ript } from "@/app/api/recognize/route";
import { MODELS } from "@/lib/const";
import { UploadType } from "@/types";

export default function Home() {
  const [files, setFiles] = useState<FileList | null>(null);
  const [analysis, setAnalysis] = useState<Script[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadType, setUploadType] = useState<UploadType>("pdf");
  const [model, setModel] = useState(MODELS.OpenAI[0].id);
  const [pdfImages, setPdfImages] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [isLoadingImages, setIsLoadingImages] = useState(false);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      if (uploadType === "pdf") {
        const file = e.target.files[0];
        if (file && file.type === "application/pdf") {
          setFiles(e.target.files);
          // Convert PDF to images
          setIsLoadingImages(true);
          try {
            const formData = new FormData();
            formData.append("file", file);

            const response = await fetch("/api/pdf-to-images", {
              method: "POST",
              body: formData,
            });

            if (!response.ok) {
              throw new Error("PDF画像変換に失敗しました");
            }

            const data = await response.json();
            setPdfImages(data.images);
          } catch (err) {
            setError(
              err instanceof Error ? err.message : "PDF変換エラーが発生しました"
            );
            setPdfImages([]);
          } finally {
            setIsLoadingImages(false);
          }
          setAnalysis([]);
          setCurrentPage(0);
        } else {
          setError("PDFファイルを選択してください");
          setFiles(null);
          setPdfImages([]);
          setAnalysis([]);
          setCurrentPage(0);
        }
      } else {
        const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
        const areValidImages = Array.from(e.target.files).every((file) =>
          allowedTypes.includes(file.type)
        );
        if (areValidImages) {
          setFiles(e.target.files);
          setError(null);
          setPdfImages([]);
          setAnalysis([]);
          setCurrentPage(0);
        } else {
          setError("対応する画像ファイルのみを選択してください");
          setFiles(null);
          setPdfImages([]);
          setAnalysis([]);
          setCurrentPage(0);
        }
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (pdfImages.length === 0) return;

    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("model", model);
      formData.append("image", pdfImages[currentPage]);

      const response = await fetch("/api/recognize", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("ファイルの解析に失敗しました");
      }

      const data = await response.json();
      setAnalysis((prev) => {
        prev[currentPage] = data.analysis;
        return prev;
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "エラーが発生しました");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex flex-col items-center gap-6">
          <ModelSelector model={model} setModel={setModel} />
          {/* Upload Type Selection */}
          <div className="grid grid-cols-2 gap-4 w-full max-w-md">
            <button
              type="button"
              onClick={() => {
                setUploadType("pdf");
                setFiles(null);
              }}
              className={`p-4 rounded-lg border-2 transition-all ${
                uploadType === "pdf"
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="text-center">
                <div className="font-medium mb-2">PDF</div>
                <div className="text-sm text-gray-600">
                  1つのPDFファイルに対応
                </div>
              </div>
            </button>
            <button
              type="button"
              onClick={() => {
                setUploadType("images");
                setFiles(null);
              }}
              className={`p-4 rounded-lg border-2 transition-all ${
                uploadType === "images"
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="text-center">
                <div className="font-medium mb-2">画像</div>
                <div className="text-sm text-gray-600">
                  複数枚の連番画像に対応
                </div>
              </div>
            </button>
          </div>

          <FileUploader
            files={files}
            uploadType={uploadType}
            handleFileChange={handleFileChange}
          />

          <Button
            type="submit"
            disabled={pdfImages.length === 0 || isLoading || isLoadingImages}
            className="w-full max-w-md"
          >
            {isLoading
              ? "解析中..."
              : isLoadingImages
              ? "PDF変換中..."
              : "ファイルを解析"}
          </Button>
        </div>
      </form>

      <MessageDisplay error={error} />

      {pdfImages.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8"></div>
      )}
    </div>
  );
}
